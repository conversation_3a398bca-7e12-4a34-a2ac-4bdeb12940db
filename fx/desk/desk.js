import { FxElement, html, css } from '../../../fx.js';

let $00;

import { BS_ITEM } from './base-item.js';

customElements.define('fx-base-main-desktop', class FxBaseMainDesktop extends FxElement {

    static get styles() {
        return css`
            :host {
                overflow: auto;
                /* background-color: white; */
                background-size: 100px 100px, 100px 100px, 10px 10px, 10px 10px;
                background-image:
                    linear-gradient(rgba(200, 200, 200, 0.3) 1px, transparent 0),
                    linear-gradient(90deg, rgba(200, 200, 200, 0.3) 1px, transparent 0),
                    linear-gradient(rgba(200, 200, 200, 0.2) 1px, transparent 0),
                    linear-gradient(90deg, rgba(200, 200, 200, 0.2) 1px, transparent 0);
                user-select: none;
            }
            .desk {
                opacity: 0;
                transition: opacity 0.5s;
            }
            .add-link:hover {
                opacity: .7;
            }
            .add-link {
                opacity: .5;
            }
            .icon {
                opacity: .9;
                cursor: pointer;
            }
            .icon:hover {
                opacity: 1;
            }
        `
    }

    render() {
        return html`
            <div class="horizontal wrap absolute w100" style="padding-left: 16px; top: 32px; z-index: 1; width: calc(100% - 32px)">
                <fx-icon url="la-desktop-solid" class="pointer ${$00?.desk === 0 ? 'o9' : 'o3'}" size="48" @click=${e => this.setDesk(0)}></fx-icon>
                <fx-icon url="la-desktop-solid" class="pointer ${$00?.desk === 1 ? 'o9' : 'o3'}" size="48" @click=${e => this.setDesk(1)}></fx-icon>
                <fx-icon url="la-desktop-solid" fill="blue" class="pointer ${$00?.desk === 2 ? 'o9' : 'o3'}" size="48" @click=${e => this.setDesk(2)}></fx-icon>
                <fx-icon url="fc-add_row" class=" pointer ${$00?.desk === 3 ? 'o9' : 'o3'}" size="46" @click=${e => this.setDesk(3)}></fx-icon>
                <div class="flex"></div>
                <fx-icon url="cb-add-alt" fill="red" class="mt4 add-link pointer ${$00?.desk < 3 ? '' : 'hidden'}" size="42" @click=${e => this.drop('')}></fx-icon>
                <fx-icon url="fc-no_idea" class="mt3 pointer ${$00?.desk === 3 ? '' : 'hidden'} ${this.showHidden ? 'o9' : 'o3'}" size="42" @click=${e => this.showHidden = !this.showHidden} title="show hidden"></fx-icon>
                <fx-icon url="fc-refresh" class="mt3 pointer o7" size="42" @click=${this._refresh} title="refresh"></fx-icon>
                <fx-icon url="cb-save" class="pointer" .fill=${(this.toSaveDesk && $00?.desk < 3) || (this.toSaveAdd && $00?.desk === 3) ? 'red' : 'lightgray'} size="48" @click=${this._save} title="save" style="opacity: .7"></fx-icon>
            </div>
            <div class="desk flex w100 h100 overflow relative" @pointermove=${this.pointerMove} @pointerup=${this.pointerUp} @dragover=${this.dragOver} @drop=${this.drop} @click=${e => { $00.deskSelected = undefined; this.$update() }}>
                ${this.desk < 3 ? this.currentDesktop?.map((i, idx) => html`
                                <fx-base-main-desktop-cell idx=${idx} .item=${i} @pointerdown=${e => this.pointerDown(e, i)}  @pointerup=${this.pointerUp}></fx-base-main-desktop-cell>
                            `)
                : html`
                        <div class="vertical flex relative m8" style="padding-top: 32px;">
                            ${this._desktopAddCells.map(type => html`
                                ${!this.showHidden && type.hidden ? html`` : html`
                                    <div class="horizontal align">
                                        <fx-icon icon=${type.icon} size="48"></fx-icon>
                                        <label class="p8 fl bold flex">${type.label}</label>
                                        <fx-icon class="icon" icon=${!type.hidden ? 'fc-idea' : 'fc-no_idea'} @click=${e => this._clickGroupHidden(type)}></fx-icon>
                                    </div>
                                    <div class="horizontal wrap center">
                                        ${type.items.map((i, idx) => !this.showHidden && i.hidden ? html`` : html`
                                            <fx-base-main-desktop-cell-add idx=${idx} .item=${i} @pointerdown=${e => this.pointerDown(e, i)} @pointerup=${this.pointerUp}></fx-base-main-desktop-cell-add>
                                        `)}
                                    </div>
                                    `}
                            `)}
                        </div>
                    `
            }
            </div>
        `
    }

    static properties = {
        desk: { type: Number, default: 0, save: true },
        showHidden: { type: Boolean },
        toSaveAdd: { type: Boolean },
        toSaveDesk: { type: Boolean },
    }

    get _desktopAddCells() {
        return $00?.dbSelected?.desktopAddCells || this.desktopAddCells;
    }
    get currentDesktop() {
        if ($00?.dbSelected && this.desk <= 1)
            return $00.dbSelected['desktop-' + $00.desk] ||= [];
        if ($00?.sets && this.desk === 2) {
            if ($00?.dbLocal.name === 'bs-fx_demo') 
                $00.sets.desktop = demoDesktop;
            else
                $00.sets.desktop ||= [];
            return $00.sets.desktop;
        }
        return [];
    }
    desktopAddCells = [
        {
            icon: 'fc-folder',
            label: 'Item',
            hidden: false,
            items: [
                {
                    header: 'folder',
                    icon: 'fc-folder',
                    label: 'Folder',
                    info: 'Пустая папка'
                },
                {
                    header: 'info',
                    icon: 'fc-info',
                    label: 'Вкладка info',
                    info: 'Видимость вкладки инфо'
                },
                {
                    header: 'events',
                    icon: 'fc-overtime',
                    label: 'События',
                    info: 'Возможность добавлять события'
                },
            ]
        },
        {
            icon: 'fc-info',
            label: 'INFO - jupyter cells',
            items: [
                {
                    header: 'link',
                    icon: 'fc-survey',
                    label: 'Документы (ссылки или встраиваемые)',
                    info: 'pdf, html, audio. video ...'
                },
                {
                    header: 'html',
                    icon: 'fc-diploma_2',
                    label: 'HTML редактор',
                    info: 'Различные HTML редакторы (pell, suneditor, cke, jodit ...)'
                },
                {
                    header: 'cherry-md',
                    icon: 'fc-diploma_1',
                    label: 'Cherry markdown',
                    info: 'Расширенный markdown редактор'
                },
                {
                    header: 'galleries',
                    icon: 'fc-multiple_cameras',
                    label: 'Фото галлерея',
                    info: ''
                },
                {
                    header: 'code',
                    icon: 'fc-android_os',
                    label: 'Code редактор',
                    info: 'Редакторы кода (ace, monaco) с темами для различных языков'
                },
                {
                    header: 'executable',
                    icon: 'fc-puzzle',
                    label: 'RunTime editor',
                    info: 'HTML, CSS, Javascript редактор с выполнением в реальном времени'
                },
                {
                    header: 'to-do',
                    icon: 'fc-todo_list',
                    label: 'ToDo - список дел',
                    info: ''
                },
                {
                    header: 'svg',
                    icon: 'fc-radar_plot',
                    label: 'SVG редактор',
                    info: ''
                },
                {
                    header: 'excalidraw',
                    icon: 'fc-parallel_tasks',
                    label: 'Редактор диаграмм',
                    info: ''
                },
                {
                    header: 'epub',
                    icon: 'fc-reading_ebook',
                    label: 'Epub читалка',
                    info: ''
                },
                {
                    header: 'money',
                    icon: 'fc-currency_exchange',
                    label: 'Деньги',
                    info: 'Денежный учет (приходы, расходы, долги, планирование)'
                },
                {
                    header: 'family-tree',
                    icon: 'fc-conference_call',
                    label: 'Семейное дерево',
                    info: 'простое семейное дерево'
                },
                {
                    header: 'jspreadsheet',
                    icon: 'fc-grid',
                    label: 'JSpreadsheet',
                    info: 'электронная таблица'
                },
                {
                    header: 'spreadsheet',
                    icon: 'fc-data_sheet',
                    label: 'Spreadsheet',
                    info: 'электронная таблица'
                }

            ]
        },
        {
            icon: 'fc-conference_call',
            label: 'Family-tree (Семейное дерево)',
            items: [
                {
                    header: 'man',
                    icon: 'fc-businessman',
                    label: 'Мужчина',
                },
                {
                    header: 'woman',
                    icon: 'fc-businesswoman',
                    label: 'Женщина',
                },
                {
                    header: 'parents',
                    icon: 'parents',
                    label: 'Родители',
                },
                {
                    header: 'wedding couple',
                    icon: 'weddingCouple',
                    label: 'Семейная пара',
                    run: 'couple'
                }
            ]
        },
        {
            icon: 'fc-sports_mode',
            label: 'Healthy Lifestyle - Дневник ЗОЖ',
            items: [
                {
                    header: 'hls total',
                    icon: 'fc-combo_chart',
                    label: 'Дневник ЗОЖ',
                    info: 'Итоговый результат за период'
                },
                {
                    header: 'hls day',
                    icon: 'fc-sports_mode',
                    label: 'Дневник ЗОЖ за день',
                },
            ]
        },
        {
            icon: 'fc-currency_exchange',
            label: 'Money - Деньги',
            items: [
                {
                    header: 'money-total',
                    icon: 'moneyTotal',
                    label: 'Деньги суммарно',
                    info: 'Итоговый результат движения денег'
                },
                {
                    header: 'money-period',
                    icon: 'fc-currency_exchange',
                    label: 'Деньги за период',
                    info: 'Денежный учет (приходы, расходы, долги, планирование)'
                },
            ]
        }
    ]

    firstUpdated() {
        super.firstUpdated();
        $00 = FX.$00;
        $00.desk = this.desk || 0;
        $00.desktop = this;
        setTimeout(() => {
            this.$qs('.desk').style.opacity = 1;
        }, 1000);
    }

    setDesk(d) {
        $00.desk = this.desk = d;
        this.$update();
    }

    pointerDown(e, i) {
        if (this.enabledMove !== e.target) {
            this.enabledMove = undefined;
            return;
        }
        e.preventDefault();
        this.cell = i;
        this.pos3 = e.clientX;
        this.pos4 = e.clientY;
        this.isMove = true;
        this.enabledMove = undefined;
    }
    pointerMove(e) {
        if (this.isMove) {
            e.preventDefault();
            this.pos1 = this.pos3 - e.clientX;
            this.pos2 = this.pos4 - e.clientY;
            this.pos3 = e.clientX;
            this.pos4 = e.clientY;
            this.cell.top -= this.pos2;
            this.cell.left -= this.pos1;
            this.toSaveDesk = true;
            this.$update();
        }
    }
    pointerUp(e) {
        if (this.isMove) {
            this.isMove = false;
            if (!this.cell) return;
            this.cell.top = Math.trunc(this.cell.top / 10) * 10;
            this.cell.left = Math.trunc(this.cell.left / 10) * 10;
            this.cell = undefined;
            this.$update();
        }
    }
    dragOver(e) {
        e.preventDefault();
    }
    drop(e) {
        let _id = e?.dataTransfer?.getData('text/plain') || $00.bsSelected._id,
            selected = $00.fxFlat[_id];
        this.currentDesktop ||= [];
        let length = this.currentDesktop.length;
        let cell = $00.deskSelected ? { ...$00.deskSelected } : length ? { ...this.currentDesktop[length - 1] } : {
            backColor: '#ffffff',
            fontSize: 'medium',
            header: '',
            headerColor: '#333333',
            iconName: '',
            iconColor: '#555555',
            iconSVG: '',
            labelAlign: 'left',
            labelClass: '',
            labelColor: '#555555',
            scaleX: 3,
            scaleY: 1,
            selectPanel: ''
        }
        if (e) {
            if (this.isDropCopy) {
                cell.left = Math.trunc((e.layerX - 8) / 10) * 10;
                cell.top = Math.trunc((e.layerY - 6) / 10) * 10;
            } else {
                cell.left = Math.trunc(e.layerX / 10) * 10;
                cell.top = Math.trunc(e.layerY / 10) * 10;
            }
        } else {
            cell.left = cell.top = 100;
        }
        if (!this.isDropCopy) {
            cell.iconName = selected.icon;
            cell.label = selected.label;
            cell.header = selected.label;
            cell._id = selected._id;
        }
        cell.iconName ||= 'cb-information';
        cell.selectPanel ||= '';
        cell.link = '';
        cell.header ||= ' ';
        cell.headerColor ||= '';
        this.currentDesktop.push(cell);
        this.toSaveDesk = true;
        this.isDropCopy = false;
        this.$update();
    }
    _clickGroupHidden(i) {
        i.hidden = !i.hidden;
        this.toSaveAdd = true;
        this.$update();
    }
    _refresh() {
        if ($00?.desk === 3) {
            this.desktopAddCells.map(i => {
                i.hidden = false;
                i.items.map(j => {
                    j.hidden = false;
                })
            })
            $00.dbSelected.desktopAddCells = this.desktopAddCells;
            this.toSaveAdd = true;
        } else {
            $00.loadSets();
            this.toSaveDesk = false;
        }
        this.$update();
    }
    _save() {
        if (this.toSaveAdd) {
            $00.dbSelected.desktopAddCells = this._desktopAddCells;
        }
        this.toSaveDesk = false;
        this.toSaveAdd = false;
        $00.saveSets(false);
        this.$update();
    }
})
customElements.define('fx-base-main-desktop-cell-add', class FxWikiMainDesktopCellAdd extends FxElement {
    static get styles() {
        return css`
            :host {
                box-sizing: border-box;
                padding: 4px;
            }
            .cell {
                box-sizing: border-box;
                border-radius:2px;
                background: var(--fxs-surface-light);
                width: 280px;
                height: 90px;
            }
            .header {
                color: #333;
            }
            .icon {
                opacity: .5;
            }
            .icon:hover {
                opacity: 1;
            }
        `
    }

    render() {
        const i = this.item;
        return html`
            <div draggable="true" class="cell vertical shadow m2 gray">
                <div class="bg4 horizontal brb mb2 relative">
                    <fx-icon class="icon" an="btn" icon=${i.hidden ? 'fc-no_idea' : 'fc-idea'} size="32" @click=${e => this._clickItemHidden(i)}></fx-icon>
                    <div class="header center horizontal flex">${this.item?.header || ''}</div>
                    <fx-icon class="icon" an="btn" icon="fc-plus" size="32" @click=${e => run(i.header)}></fx-icon>
                </div>
                <div class="horizontal center">
                    <fx-icon icon=${i.icon} size="48"></fx-icon>
                    <div>
                        <div class="w100" style="text-wrap: wrap">${this.item?.label}</div>
                        <div class="fxxs mt2">${this.item?.info}</div>
                    </div>
                    <div class="flex"></div>
                </div>
            </div>
        `
    }

    static get properties() {
        return {
            item: { type: Object, default: {} }
        }
    }

    _clickItemHidden(i) {
        i.hidden = !i.hidden;
        $00.desktop.toSaveAdd = true;
        $00.desktop.$update();
    }
})

// fxAdd(_id, iff, is, icon, iconFill, label)
export const run = async (cmd) => {
    if (!cmd) return;
    let _id = $00.fxSelected._id,
        fxSelected = $00.fxSelected,
        bsSelected = $00.bsSelected;
    const cmds = {
        'folder': async () => {
            await $00.fxAdd(_id);
        },
        'info': async () => {
            await $00.fxAdd(_id, 'ifInfo');
        },
        'man': async () => {
            let { bsAdd } = await $00.fxAdd(_id, '', 'man', 'fc-businessman');
            bsAdd.doc.photo = '/fx/persona/man.jpg';
        },
        'woman': async () => {
            let { bsAdd } = await $00.fxAdd(_id, '', 'woman', 'fc-businesswoman');
            bsAdd.doc.photo = '/fx/persona/woman.jpg';
        },
        'parents': async () => {
            let pra = fxSelected.label.includes('прапрапра') ? '...' : fxSelected.label.includes('прапра') ? 'прапрапра' : fxSelected.label.includes('пра') ? 'прапра' : 'пра',
                label = fxSelected.label === 'отец' || fxSelected.label === 'мать' ? 'дед' : fxSelected.label.includes('дед') || fxSelected.label.includes('бабушка') ? pra + 'дед' : 'отец';
            let add = await $00.fxAdd(_id, '', 'man', 'fc-businessman', '', label);
            const m = add.bsAdd
            m.doc.photo = '/fx/persona/man.jpg';
            label = fxSelected.label === 'мать' || fxSelected.label === 'отец' ? 'бабушка' : fxSelected.label.includes('бабушка') || fxSelected.label.includes('дед') ? pra + 'бабушка' : 'мать';
            add = await $00.fxAdd(_id, '', 'woman', 'fc-businesswoman', '', label);
            const w = add.bsAdd;
            w.doc.photo = '/fx/persona/woman.jpg';
            bsSelected.father = m;
            bsSelected.doc.father = m._id;
            bsSelected.mother = w;
            bsSelected.doc.mother = w._id;
            w.spouses = {};
            w.spouses[m._id] = m;
            w.doc.spouses = {};
            w.doc.spouses[m._id] = { _id: m._id };
            m.spouses = {};
            m.spouses[w._id] = w;
            m.doc.spouses = {};
            m.doc.spouses[w._id] = { _id: w._id };
            bsSelected.items.push(m, w);
            bsSelected.expanded = true;
        },
        'wedding couple': async () => {
            let label = 'муж';
            let add = await $00.fxAdd(_id, '', 'man', 'fc-businessman', '', label);
            const m = add.bsAdd
            m.doc.photo = '/fx/persona/man.jpg';
            label = 'жена';
            add = await $00.fxAdd(_id, '', 'woman', 'fc-businesswoman', '', label);
            const w = add.bsAdd;
            w.doc.photo = '/fx/persona/woman.jpg';
            w.spouses = {};
            w.spouses[m._id] = m;
            w.doc.spouses = {};
            w.doc.spouses[m._id] = { _id: m._id };
            m.spouses = {};
            m.spouses[w._id] = w;
            m.doc.spouses = {};
            m.doc.spouses[w._id] = { _id: w._id };
            bsSelected.items.push(m, w);
            bsSelected.expanded = true;
        },
        'hls day': async () => {
            // fxAdd(_id, iff, is, icon, iconFill, label)
            await $00.fxAdd(_id, '', 'hlsDay', 'fc-sports_mode', '', FX.dates().short);
        },
        'hls total': async () => {
            await $00.fxAdd(_id, '', 'hlsTotal', 'fc-combo_chart', '', FX.dates().monthStr);
        },
        'money-period': async () => {
            await $00.fxAdd(_id, '', 'money', 'fc-currency_exchange', '', FX.dates().monthStr);
        },
        'money-total': async () => {
            await $00.fxAdd(_id, '', 'moneyTotal', 'moneyTotal', '', FX.dates().year);
        },
        'events': async () => {
            await $00.fxAdd(_id, 'ifEvents');
        },
        'calendar': () => {
            if (fxSelected.if?.ifCalendar) {
                fxSelected.if.ifCalendar = bsSelected.if.ifCalendar = undefined;
                $00.fxSet('icon', undefined, _id);
            } else {
                fxSelected.if ||= {};
                bsSelected.if ||= {};
                fxSelected.if.ifCalendar = bsSelected.if.ifCalendar = true;
                $00.fxSet('icon', 'fc-planner', _id);
            }
        },
        'form0': async () =>  {
            await $00.fxAdd(_id, 'ifForm', '', 'fc-automatic', '', '...', 'ifForm0');
        },
        'form': async () =>  {
            await $00.fxAdd(_id, 'ifForm', '', 'fc-template');
        }
    }
    if (['link', 'galleries', 'cherry-md', 'html', 'doc', 'epub', 'markdown', 'code', 'executable', 'svg', 'excalidraw', 'family-tree', 'to-do', 'money', 'spreadsheet', 'jspreadsheet'].includes(cmd)) {
        let { fxAdd } = await $00.fxAdd(_id, 'ifInfo', '', '', '', '... ' + cmd);
        $00.fxSelected = fxAdd;
        $00.scrollTo(fxAdd._id);
        await FX.sleep(100);
        $00.mainInfo?.makeInfo(cmd, fxAdd);
    } else {
        cmds[cmd] && cmds[cmd]();
    }
}

customElements.define('fx-base-main-desktop-cell', class FxWikiMainDesktopCell extends FxElement {
    static get styles() {
        return css`
            :host {
                box-sizing: border-box;
            }
            .cell {
                position: absolute;
                cursor: pointer;
                box-sizing: border-box;
                border-radius:2px;
            }
            label {
                font-family: Arial;
                margin: 0 2px;
                text-overflow: ellipsis;
                overflow: hidden;
                cursor: pointer;
            }
            .btn {
                opacity: 0.2;
            }
            .btn:hover {
                opacity: .8;
            }
            .idx {
                font-size: xx-small;
            }
        `
    }

    render() {
        return html`
            <style>
                .cell {
                    background: ${this.item?.backColor || '#ffffff'};
                    width: ${(this.item?.scaleX || 1) * 76 + 'px'};
                    height: ${(this.item?.scaleY || 1) * 76 + 'px'};
                }
                label {
                    font-size: ${this.item?.fontSize || 'xx-small'};
                    color: ${this.item?.labelColor || '#555555'};
                    text-align: ${this.item?.labelAlign || ''};
                }
                .header {
                    color: ${this.item?.headerColor || '#333333'};
                }
            </style>
            <div draggable=${$00.desktop.enabledMove === this} class="cell vertical shadow m2 gray" style="z-index: ${this.selected ? 1 : 0}; left: ${this.item?.left || 10}px; top: ${this.item?.top || 10}px;" @click=${this.click} @dblclick=${this.dblClick}>
                <div class="bg4 horizontal brb mb2">
                    <fx-icon class="mt1" url=${this.item?.link ? 'fc-link' : 'fc-info'} @pointermove=${this.enableMove} @pointerdown=${this.dropCopy}></fx-icon>
                    <div class="header w100 ellipsis pl2 pt4 pr2" @pointerdown=${this.enableMove}>${this.item?.header || ''}</div>
                    <fx-icon class="btn ${this.selected ? '' : 'hidden'}" url="ev-o-settings-2-outline" fill="#555" @pointerdown=${this.showPG}></fx-icon>
                    <fx-icon class="btn ${this.selected ? '' : 'hidden'}" url="ev-o-close-outline" fill="red" @click=${this.deleteCell}></fx-icon>
                </div>
                <div class="horizontal flex align" style="overflow: hidden; align-items: start">
                    <fx-icon class="${this.item?.iconName || this.item?.iconSVG ? '' : 'hidden'}" .svg=${this.item?.iconSVG} .url=${this.item?.iconName} size="48" .fill=${this.item?.iconColor || '#555'} @click=${this.dblClick}></fx-icon>
                    <label class="flex ml4 mr4 ${this.item?.labelClass || ''}">${this.item?.label}</label>
                </div>
            </div>
        `
    }

    static get properties() {
        return {
            item: { type: Object, default: {} },
            idx: { type: Number, default: 0 },
            selected: { type: Boolean }
        }
    }
    get cell() { return this.$qs('.cell') }
    get selected() { return $00.deskSelected === this.item }

    firstUpdated() {
        super.firstUpdated();
        this.item.iconName ||= 'cb-information';
        this.$update();
    }

    async showPG(e) {
        e.stopPropagation();
        e.preventDefault();
        this.$listen('changedInPropertyGrid', () => {
            $00.desktop.toSaveDesk = true;
            this.$update();
        })
        FX.closePG('desktop-pg');
        FX.showPG({ io: this.item, showButtons: false, group: false, sort: 'ascending' }, { label: this.item.header, hideBottom: true }, 'desktop-pg');
        this.$update();
    }
    dropCopy(e) {
        $00.deskSelected = this.item;
        e.stopPropagation();
        $00.desktop.isDropCopy = true;
        this.$update();
    }
    enableMove() {
        $00.deskSelected = this.item;
        $00.desktop.enabledMove = this;
    }
    deleteCell(e) {
        $00.desktop.currentDesktop.splice(this.idx, 1);
        $00.desktop.toSaveDesk = true;
        this.$update();
    }
    click(e) {
        e.stopPropagation();
        e.preventDefault();
        $00.deskSelected = this.item;
        this.$update();
    }
    dblClick(e) {
        e.stopPropagation();
        e.preventDefault();
        $00.deskSelected = this.item;
        setTimeout(() => {
            if (this.item.selectPanel)
                $00.main.selectPanel(this.item.selectPanel);
        }, 100)
        $00.deskSelected = undefined;
        this.$update();
        if (this.item.link) {
            window.open(this.item.link, '_blank');//.focus();
            return;
        }
        $00.isScrollTo = true;
        $00.fxSelected = $00.fxFlat[this.item._id];
    }
})

const weddingCouple = `
<svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 192.218 192.218" xml:space="preserve">
<g>
	<g>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" points="139.868,59.515 150.065,60.765 153.003,44.613 
			142.807,43.364 		"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#D9ECED;" d="M42.867,16.54c24.19-6.091,51.487,18.689,62.622,55.964
			C95.45,85.526,81.076,95.42,63.821,99.765c-17.254,4.345-34.6,2.437-49.607-4.279C6.369,57.383,18.676,22.631,42.867,16.54z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M75.618,72.693c4.62-1.206,14.358-5.043,17.962-8.06
			l2.035,3.481C92.585,72,82.841,77.31,78.458,79.754L75.618,72.693z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M61.695,68.729c5.153,1.444,11.526,2.63,18.295,3.255
			l-0.798,7.444c-6.294,0.353-11.99,0.216-15.778-0.386C52.816,77.36,50.42,65.568,61.695,68.729z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M91.03,63.271c0.658-1.295,1.477-1.362,2.172-0.606
			c0.722-2.026,2.817-2.923,3.824-2.411c1.959,0.995,2.451,3.96,1.099,6.621c-1.353,2.662-4.036,4.013-5.996,3.018
			C90.17,68.897,89.678,65.933,91.03,63.271z"/>
		
			<rect x="50.657" y="59.49" transform="matrix(-0.968 0.2509 -0.2509 -0.968 128.1275 119.0598)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" width="11.633" height="16.416"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M62.296,105.684l-9.273-19.877l-1.808-15.216
			c3.843-0.349,7.594-1.342,11.261-2.918c2.957,0.66,7.304,6.845,8.068,12.755l3.331,22.255L62.296,105.684z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M70.589,101.248c24.945,0,45.366,30.695,47.062,69.56
			c-12.914,10.177-29.269,16.262-47.062,16.262c-17.793,0-29.428-8.23-42.341-18.407C29.943,129.798,45.644,101.248,70.589,101.248z
			"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M53.023,85.807c1.392-0.335,9.265-11.864,15.74-11.179
			c0.869,1.82,1.524,3.825,1.78,5.8l3.298,22.029l0.033,0.226l-11.579,3.001L53.023,85.807z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M62.975,76.206c9.295,8.038,24.634,17.502,39.36,21.626
			l-1.217,4.013c-14.233-2.01-33.279-9.635-41.144-14.983C50.58,80.473,53.599,68.097,62.975,76.206z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M74.49,192.218c4.62,0,8.388-1.893,8.388-4.213
			c0-2.319-3.768-4.212-8.388-4.212c-4.619,0-8.387,1.893-8.387,4.212C66.104,190.325,69.871,192.218,74.49,192.218z"/>
		<g>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" d="M127.086,186.817l-2.566-55.118l-0.018-12.87l-0.013-4.289
				h14.801c-1.082,24.093-2.165,48.184-3.247,72.277C133.057,186.817,130.07,186.817,127.086,186.817z"/>
			<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" points="124.488,114.54 124.694,114.54 124.501,118.831 
				124.501,118.829 			"/>
		</g>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M126.845,192.218c5.065,0,9.197-2.075,9.197-4.619
			c0-2.544-4.132-4.619-9.197-4.619c-5.065,0-9.196,2.075-9.196,4.619C117.648,190.143,121.779,192.218,126.845,192.218z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M123.789,69.206c-5.521-0.494-16.764-4.198-21.469-6.96
			l-1.662,4.376c4.216,3.867,15.746,9.256,21.233,11.213L123.789,69.206z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M140.004,63.063c-5.759,2.332-12.958,4.524-20.687,6.12
			l1.882,8.484c7.307-0.406,13.86-1.3,18.152-2.482C151.361,71.875,152.603,57.961,140.004,63.063z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M102.724,60.413c-1.331-1.033-2.224-0.699-2.58,0.44
			c-1.762-1.771-4.405-1.674-5.21-0.639c-1.563,2.016-0.612,5.371,2.126,7.497c2.737,2.125,6.225,2.215,7.789,0.199
			C106.413,65.895,105.462,62.539,102.724,60.413z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M106.323,96.788c1.358,0.72,1.415,1.587,0.602,2.31
			c2.132,0.802,3.042,3.036,2.481,4.093c-1.09,2.055-4.236,2.522-7.028,1.041c-2.793-1.48-4.174-4.347-3.084-6.402
			C100.384,95.773,103.53,95.308,106.323,96.788z"/>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" points="140.652,53.995 151.24,55.51 150.439,61.107 
			138.877,63.977 		"/>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" points="122.327,112.566 146.868,116.076 146.48,121.085 
			121.635,117.531 		"/>
		<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" points="146.611,119.419 150.971,57.662 142.723,58.286 
			126.697,87.02 121.859,115.763 		"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" d="M141.539,55.326c-3.98,18.962-4.857,28.249-14.379,33.752
			l1.149-8.203l10.411-19.682l1.933-7.198L141.539,55.326z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" d="M141.619,52.967l-1.692,12.369l-0.836,1.848l-8.241,17.327
			l-5.694,9.088l2.931-13.361l10.634-19.044l1.501-6.259c0.229-0.952,0.123-4.76,0.123-4.76L141.619,52.967z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" d="M139.443,64.877c-7.162,11.414-20.203,26.266-34.317,35.32
			l2.564,3.816c14.277-6.681,31.795-20.794,38.323-28.929C153.81,65.368,146.668,53.363,139.443,64.877z"/>
		<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" d="M104.561,96.746c-1.381,0.967-1.325,1.918-0.343,2.595
			c-2.212,1.16-2.901,3.714-2.149,4.788c1.462,2.09,4.95,2.173,7.789,0.186c2.84-1.987,3.956-5.292,2.494-7.383
			C110.889,94.842,107.4,94.759,104.561,96.746z"/>
		<g>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" d="M154.549,185.254l-20.446-51.25l-4.226-12.156l-1.414-4.05
				l13.987-4.839c6.854,23.122,13.708,46.244,20.563,69.367C160.192,183.302,157.37,184.278,154.549,185.254z"/>
			<polygon style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" points="128.463,117.798 128.657,117.73 129.878,121.85 
				129.877,121.848 			"/>
		</g>
		
			<ellipse transform="matrix(-0.327 -0.945 0.945 -0.327 29.2796 392.9934)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#1A1A1A;" cx="154.577" cy="186.071" rx="4.619" ry="9.197"/>
		
			<ellipse transform="matrix(0.3481 -0.9375 0.9375 0.3481 -141.5662 163.8561)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" cx="47.03" cy="183.715" rx="4.213" ry="8.387"/>
		<g>
			
				<ellipse transform="matrix(0.4068 -0.9135 0.9135 0.4068 69.7285 183.6045)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="176.242" cy="38.11" rx="5.78" ry="3.699"/>
			
				<ellipse transform="matrix(-0.0363 -0.9993 0.9993 -0.0363 102.7389 149.9801)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="123.684" cy="25.454" rx="5.781" ry="3.699"/>
			
				<ellipse transform="matrix(0.1902 -0.9817 0.9817 0.1902 87.1974 173.969)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="149.051" cy="34.129" rx="20.796" ry="26.979"/>
			
				<ellipse transform="matrix(0.1902 -0.9817 0.9817 0.1902 84.4548 163.5784)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="141.381" cy="30.597" rx="2.624" ry="1.499"/>
			
				<ellipse transform="matrix(0.1902 -0.9817 0.9817 0.1902 94.4271 181.9119)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="157.484" cy="33.716" rx="2.624" ry="1.499"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M172.931,19.088c-13.024-3.085-38.182,13.643-50.558,5.591
				c5.658-15.662,24.02-21.512,37.684-16.426C165.101,10.129,169.54,12.703,172.931,19.088z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M164.05,11.641c-4.798,10.628,3.711,25.086,12.192,26.469
				C179.388,29.184,173.471,15.208,164.05,11.641z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" d="M156.321,43.411c-3.952,0-13.266-1.872-17.482-3.387
				C142.283,46.283,151.661,48.295,156.321,43.411z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M172.839,19.56c-17.68-7.8-47.153,10.951-50.322-11.739
				c8.51,5.233,10.184,2.012,19.772-0.934C151.961,3.915,165.3,5.361,172.839,19.56z"/>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M174.265,24.602C159.27,12.413,125.893,22.723,128.823,0
				c6.827,7.293,9.292,4.628,19.318,4.316C158.254,4.002,170.739,8.917,174.265,24.602z"/>
		</g>
		<g>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 27.4434 86.9165)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" cx="46.841" cy="33.001" rx="15.363" ry="15.517"/>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 25.4351 91.9061)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#F5F0F0;" cx="47.738" cy="36.261" rx="11.981" ry="17.54"/>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 24.7809 93.5307)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" cx="48.03" cy="37.323" rx="10.88" ry="19.508"/>
			
				<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 17.5442 110.9603)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="51.053" cy="48.795" rx="19.555" ry="22.062"/>
			<g>
				
					<ellipse transform="matrix(-0.2652 -0.9642 0.9642 -0.2652 29.2611 114.7157)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="58.34" cy="46.209" rx="2.467" ry="1.409"/>
				
					<ellipse transform="matrix(-0.2651 -0.9642 0.9642 -0.2651 6.4928 105.5464)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#323232;" cx="43.469" cy="50.299" rx="2.467" ry="1.41"/>
			</g>
			<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#C79369;" d="M61.565,53.555c-3.531,1.748-12.677,4.195-17.112,4.707
				C50.297,62.329,59.563,59.978,61.565,53.555z"/>
			
				<ellipse transform="matrix(-0.476 -0.8794 0.8794 -0.476 -4.6912 106.6855)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="29.438" cy="54.74" rx="5.761" ry="3.687"/>
			
				<ellipse transform="matrix(-0.0408 -0.9992 0.9992 -0.0408 32.4031 117.4178)" style="fill-rule:evenodd;clip-rule:evenodd;fill:#E7B791;" cx="72.564" cy="43.155" rx="5.761" ry="3.687"/>
			<g>
				<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M50.418,27.82c11.975,3.284,20.086,17.828,25.788,14.232
					C72.165,29.776,58.548,23.654,50.418,27.82z"/>
				<path style="fill-rule:evenodd;clip-rule:evenodd;fill:#5E3A1D;" d="M62.125,27.505c-17.185,3.736-21.34,29.617-36.561,27.665
					C21.797,36.275,40.678,22.316,62.125,27.505z"/>
			</g>
		</g>
	</g>
</g>
</svg>
`

const parents = `
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 503.467 503.467" xml:space="preserve">
<g transform="translate(1 9)">
	<path style="fill:#FFD0A1;" d="M412.867,123.267V157.4c0,25.6-17.067,59.733-42.667,59.733S327.534,183,327.534,157.4v-34.133
		c0,0,25.6,0,34.133-25.6C370.201,123.267,412.867,123.267,412.867,123.267z M216.601,89.134V106.2c0,4.267-3.413,8.533-8.533,8.533
		c0,17.067-8.533,34.133-8.533,34.133c-8.533,17.067-17.92,34.133-51.2,34.133s-42.667-17.067-51.2-34.133
		c0,0-8.533-17.067-8.533-34.133c-4.267,0-8.533-4.267-8.533-8.533V89.134c0-4.267,4.267-8.533,8.533-8.533v-8.533
		c0-23.893,18.773-42.667,42.667-42.667h34.133c23.893,0,42.667,18.773,42.667,42.667V80.6
		C213.187,80.6,216.601,84.867,216.601,89.134z"/>
	<path style="fill:#C49AE9;" d="M370.201,285.4c42.667,0,42.667-51.2,42.667-51.2c17.067,0,30.72,2.56,42.667,7.68l0,0
		c29.867,11.947,42.667,36.693,42.667,60.587V439l-51.2,8.533v8.533h-153.6V439V268.334c0-9.387-3.413-17.92-9.387-24.747
		l0.853-0.853c20.48-8.533,41.813-8.533,42.667-8.533C327.534,234.2,327.534,285.4,370.201,285.4"/>
	<path style="fill:#AE938D;" d="M412.867,157.4v-34.133c0,0-42.667,0-51.2-25.6c-8.533,25.6-34.133,25.6-34.133,25.6V157.4
		c0,25.6,17.067,59.733,42.667,59.733S412.867,183,412.867,157.4 M438.467,97.667l17.067,144.213
		c-11.947-4.267-25.6-7.68-42.667-7.68c0,0,0,51.2-42.667,51.2s-42.667-51.2-42.667-51.2c-0.853,0-22.187,0-42.667,8.533
		l17.067-145.067c2.56-25.6,34.133-51.2,59.733-51.2h17.067C404.334,46.467,436.761,72.067,438.467,97.667"/>
	<polyline style="fill:#AAB1BA;" points="187.587,225.667 189.294,225.667 148.334,421.934 107.374,225.667 109.08,225.667 	"/>
	<path style="fill:#80D6FA;" d="M148.334,234.2c5.12,0,8.533-3.413,8.533-8.533c0-5.12-3.413-8.533-8.533-8.533
		s-8.533,3.413-8.533,8.533C139.801,230.787,143.214,234.2,148.334,234.2z M54.467,447.534L3.267,439V268.334
		c0-34.133,42.667-51.2,102.4-51.2l1.707,8.533l40.96,196.267l40.96-196.267l1.707-8.533c42.667,0,76.8,8.533,93.013,26.453
		c5.973,6.827,9.387,15.36,9.387,24.747V439l-51.2,8.533v8.533H54.467V447.534z"/>
</g>
<path style="fill:#51565F;" d="M448.001,469.334L448.001,469.334c-2.56,0-4.267-1.707-4.267-4.267l0,0l0.853-136.533
	c0-2.56,1.707-4.267,4.267-4.267l0,0c2.56,0,4.267,1.707,4.267,4.267l0,0l-0.853,136.533
	C452.267,467.627,450.561,469.334,448.001,469.334z M243.201,469.334c-2.56,0-4.267-1.707-4.267-4.267V328.534
	c0-2.56,1.707-4.267,4.267-4.267s4.267,1.707,4.267,4.267v136.533C247.467,467.627,245.761,469.334,243.201,469.334z
	 M55.467,469.334c-2.56,0-4.267-1.707-4.267-4.267V328.534c0-2.56,1.707-4.267,4.267-4.267c2.56,0,4.267,1.707,4.267,4.267v136.533
	C59.734,467.627,58.027,469.334,55.467,469.334z M499.201,452.267c-2.56,0-4.267-1.707-4.267-4.267V311.467
	c0-16.213-5.973-30.72-17.067-41.813C464.214,256,443.734,248.32,418.134,248.32c-1.707,18.773-11.093,48.64-42.667,51.2v46.933
	c0,2.56-1.707,4.267-4.267,4.267s-4.267-1.707-4.267-4.267V299.52c-33.28-2.56-42.667-36.693-42.667-55.467
	c0-2.56,1.707-4.267,4.267-4.267l0,0c2.56,0,4.267,1.707,4.267,4.267c0,1.707,0.853,46.933,38.4,46.933s38.4-45.227,38.4-46.933
	c0-2.56,1.707-4.267,4.267-4.267c29.867,0,53.76,8.533,69.973,23.893c12.8,12.8,19.627,29.867,19.627,47.787V448
	C503.467,450.56,501.761,452.267,499.201,452.267z M294.401,452.267c-2.56,0-4.267-1.707-4.267-4.267V277.334
	c0-9.387-3.413-17.067-10.24-23.893c-14.507-14.507-44.373-23.04-84.48-23.04l-41.813,201.387v0.853
	c0,0.853-0.853,0.853-0.853,1.707c-0.853,0.853-0.853,0.853-1.707,0.853c-0.853,0-0.853,0-1.707,0l0,0l0,0c-0.853,0-0.853,0-1.707,0
	s-0.853-0.853-1.707-0.853c-0.853,0-0.853-0.853-0.853-1.707v-0.853L103.253,230.4c-40.107,0-69.973,8.533-84.48,23.04
	c-6.827,6.827-10.24,14.507-10.24,23.893V448c0,2.56-1.707,4.267-4.267,4.267S0,450.56,0,448V277.334
	C0,266.24,4.267,256,12.8,247.467c17.067-17.067,49.493-25.6,93.867-25.6c2.56,0,4.267,0.853,5.12,3.413l38.4,184.32l38.4-184.32
	c0.853-2.56,2.56-3.413,5.12-3.413c43.52,0,76.8,9.387,93.867,25.6c8.533,8.533,12.8,17.92,12.8,29.867V448
	C298.667,450.56,296.961,452.267,294.401,452.267z M149.334,315.734c-2.56,0-4.267-1.707-4.267-4.267v-64.853
	c-5.12-1.707-8.533-6.827-8.533-11.947c0-6.827,5.973-12.8,12.8-12.8s12.8,5.973,12.8,12.8c0,5.973-3.413,10.24-8.533,11.947v64.853
	C153.601,314.027,151.894,315.734,149.334,315.734z M149.334,230.4c-2.56,0-4.267,1.707-4.267,4.267c0,2.56,1.707,4.267,4.267,4.267
	s4.267-1.707,4.267-4.267C153.601,232.107,151.894,230.4,149.334,230.4z M371.201,230.4c-29.013,0-46.933-37.547-46.933-64v-34.133
	c0-2.56,1.707-4.267,4.267-4.267c0.853,0,23.04,0,29.867-23.04c0.853-3.413,6.827-3.413,7.68,0
	c7.68,22.187,46.933,23.04,46.933,23.04c2.56,0,4.267,1.707,4.267,4.267V166.4C418.134,192.854,400.214,230.4,371.201,230.4z
	 M332.801,136.534V166.4c0,23.04,15.36,55.467,38.4,55.467s38.4-32.427,38.4-55.467v-29.867
	c-10.24-0.853-34.987-4.267-46.933-19.627C354.987,129.707,342.187,134.827,332.801,136.534z M448.001,221.867
	c-2.56,0-4.267-1.707-4.267-4.267l-8.533-110.933c-1.707-22.187-31.573-46.933-55.467-46.933h-17.067
	c-24.747,0-53.76,24.747-55.467,46.933L298.667,217.6c0,2.56-2.56,4.267-4.267,4.267c-2.56,0-4.267-2.56-4.267-4.267l8.533-110.933
	c2.56-26.453,34.987-55.467,64-55.467h17.067c26.453,0,61.44,25.6,64,55.467l8.533,110.933
	C452.267,219.307,450.561,221.867,448.001,221.867L448.001,221.867z M149.334,196.267c-36.693,0-46.933-21.333-54.613-36.693
	c0-0.853-7.68-16.213-8.533-32.427c-5.12-1.707-8.533-6.827-8.533-11.947V98.134c0-5.973,3.413-10.24,8.533-11.947v-5.12
	c0-25.6,21.333-46.933,46.933-46.933h34.133c25.6,0,46.933,21.333,46.933,46.933v5.12c5.12,1.707,8.533,6.827,8.533,11.947V115.2
	c0,5.973-3.413,10.24-8.533,11.947c-0.853,16.213-8.533,31.573-8.533,32.427C196.267,174.934,186.027,196.267,149.334,196.267z
	 M132.267,42.667c-21.333,0-38.4,17.067-38.4,38.4V89.6c0,2.56-1.707,4.267-4.267,4.267s-4.267,1.707-4.267,4.267V115.2
	c0,2.56,1.707,4.267,4.267,4.267s4.267,1.707,4.267,4.267c0,16.213,7.68,32.427,7.68,32.427
	c9.387,18.773,17.92,31.573,47.787,31.573s38.4-12.8,47.787-31.573c0,0,7.68-16.213,7.68-32.427c0-2.56,1.707-4.267,4.267-4.267
	s4.267-1.707,4.267-4.267V98.134c0-2.56-1.707-4.267-4.267-4.267s-4.267-1.707-4.267-4.267v-8.533c0-21.333-17.067-38.4-38.4-38.4
	H132.267z M174.934,110.934h-8.533c-2.56,0-4.267-1.707-4.267-4.267c0-2.56,1.707-4.267,4.267-4.267h8.533
	c2.56,0,4.267,1.707,4.267,4.267C179.201,109.227,177.494,110.934,174.934,110.934z M132.267,110.934h-8.533
	c-2.56,0-4.267-1.707-4.267-4.267c0-2.56,1.707-4.267,4.267-4.267h8.533c2.56,0,4.267,1.707,4.267,4.267
	C136.534,109.227,134.827,110.934,132.267,110.934z"/>
</svg>
`
const usedIcons = {
    "weddingCouple": weddingCouple,
    "parents": parents
}
FX.setIcons(usedIcons);

const demoDesktop = [
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "icons",
        "headerColor": "#0000ff",
        "iconName": "fc-stack_of_photos",
        "iconColor": "#555555",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 50,
        "left": 10,
        "label": "Icons",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/fx/icons/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "dice",
        "headerColor": "#0000ff",
        "iconName": "gm-inverted-dice-3",
        "iconColor": "#ebac00",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 50,
        "left": 250,
        "label": "Dice",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/dice/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "yahtzee",
        "headerColor": "#0000ff",
        "iconName": "gm-inverted-dice-5",
        "iconColor": "#006bad",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 140,
        "left": 250,
        "label": "Yahtzee",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/dice-yahtzee/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "tester",
        "headerColor": "#0000ff",
        "iconName": "fc-settings",
        "iconColor": "#ffffff",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 50,
        "left": 490,
        "label": "Tester",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/tester/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "color-picker",
        "headerColor": "#0000ff",
        "iconName": "cb-color-palette",
        "iconColor": "#db00be",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 230,
        "left": 0,
        "label": "Color picker",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/color-picker/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "credit-calc",
        "headerColor": "#0000ff",
        "iconName": "fc-calculator",
        "iconColor": "#555555",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 320,
        "left": 0,
        "label": "Кредитный калькулятор",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/credit-calc/index.html"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "lz-string",
        "headerColor": "#0000ff",
        "iconName": "fc-biotech",
        "iconColor": "#555555",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 410,
        "left": 0,
        "label": "LZ String",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/lzstring/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "fx-jupyter",
        "headerColor": "#0000ff",
        "iconName": "fc-reading_ebook",
        "iconColor": "#555555",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 500,
        "left": 0,
        "label": "FX Jupyter",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/fx/jupyter/index.html#?lzs=N4IgNghgRgpmIC4QCsCuAHAngFxgJwH0A7Ae1yhJIGsCATGAWxJABoQBjOMAZ0QG1QqMAEtaiEAAYAjACkALABEA0gDkAbAFklACQAKSuQGFdhgOoBxAJwSAWgEEAzACUAaqxDZM6GOLRZchJxg8GxBYASe3uIwAB4w7KjY0GA+oVwEsdgAKl4+SLHxicmp4NBw0XEJSVAp7twkqHic4u4EojGIUmz1jZzaWRoAMi1sqNwwMvVEiNh4qDAAvgC6C0A"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "tetris",
        "headerColor": "#0000ff",
        "iconName": "fl-r-xx",
        "iconColor": "#555555",
        "iconSVG": "<svg viewBox=\"0 0 24 24\" fill=\"blue\" xmlns=\"http://www.w3.org/2000/svg\"> <path d=\"M9.18 12.75H3.53C3.33189 12.7474 3.14263 12.6675 3.00253 12.5275C2.86244 12.3874 2.78259 12.1981 2.78 12V6.51999C2.78 6.32108 2.85902 6.13031 2.99967 5.98966C3.14032 5.84901 3.33109 5.76999 3.53 5.76999H9.18C9.37891 5.76999 9.56968 5.84901 9.71033 5.98966C9.85098 6.13031 9.93 6.32108 9.93 6.51999V12C9.92741 12.1981 9.84756 12.3874 9.70747 12.5275C9.56737 12.6675 9.37811 12.7474 9.18 12.75ZM4.28 11.25H8.43V7.24999H4.28V11.25Z\" fill=\"blue\"/> <path d=\"M14.82 12.75H9.18C8.98109 12.75 8.79032 12.671 8.64967 12.5303C8.50902 12.3897 8.43 12.1989 8.43 12V6.52C8.42865 6.42113 8.44714 6.323 8.48435 6.2314C8.52156 6.1398 8.57676 6.05658 8.64667 5.98667C8.71659 5.91676 8.7998 5.86156 8.8914 5.82435C8.983 5.78713 9.08114 5.76865 9.18 5.77H14.82C14.9189 5.76865 15.017 5.78713 15.1086 5.82435C15.2002 5.86156 15.2834 5.91676 15.3533 5.98667C15.4232 6.05658 15.4784 6.1398 15.5156 6.2314C15.5529 6.323 15.5713 6.42113 15.57 6.52V12C15.57 12.1989 15.491 12.3897 15.3503 12.5303C15.2097 12.671 15.0189 12.75 14.82 12.75ZM9.93 11.25H14.07V7.25H9.93V11.25Z\" fill=\"blue\"/> <path d=\"M20.47 12.75H14.82C14.6219 12.7474 14.4326 12.6675 14.2925 12.5275C14.1524 12.3874 14.0726 12.1981 14.07 12V6.51999C14.07 6.32108 14.149 6.13031 14.2897 5.98966C14.4303 5.84901 14.6211 5.76999 14.82 5.76999H20.47C20.6689 5.76999 20.8597 5.84901 21.0003 5.98966C21.141 6.13031 21.22 6.32108 21.22 6.51999V12C21.2174 12.1981 21.1376 12.3874 20.9975 12.5275C20.8574 12.6675 20.6681 12.7474 20.47 12.75ZM15.57 11.25H19.72V7.24999H15.57V11.25Z\" fill=\"#blue\"/> <path d=\"M14.82 18.23H9.18C9.08114 18.2313 8.983 18.2129 8.8914 18.1756C8.7998 18.1384 8.71659 18.0832 8.64667 18.0133C8.57676 17.9434 8.52156 17.8602 8.48435 17.7686C8.44714 17.677 8.42865 17.5789 8.43 17.48V12C8.43 11.8011 8.50902 11.6103 8.64967 11.4697C8.79032 11.329 8.98109 11.25 9.18 11.25H14.82C15.0189 11.25 15.2097 11.329 15.3503 11.4697C15.491 11.6103 15.57 11.8011 15.57 12V17.48C15.5713 17.5789 15.5529 17.677 15.5156 17.7686C15.4784 17.8602 15.4232 17.9434 15.3533 18.0133C15.2834 18.0832 15.2002 18.1384 15.1086 18.1756C15.017 18.2129 14.9189 18.2313 14.82 18.23ZM9.93 16.73H14.07V12.73H9.93V16.73Z\" fill=\"#blue\"/> </svg>",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 230,
        "left": 250,
        "label": "Tetris",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/tetris/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "flips",
        "headerColor": "#0000ff",
        "iconName": "fc-gallery",
        "iconColor": "#555555",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 320,
        "left": 250,
        "label": "Filps",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/flips/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "raccoon",
        "headerColor": "#0000ff",
        "iconName": "gm-raccoon-head",
        "iconColor": "#b35600",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 500,
        "left": 250,
        "label": "Raccoon",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/raccoon/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "minesweeper",
        "headerColor": "#0000ff",
        "iconName": "fa-s-land-mine-on",
        "iconColor": "#c70000",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 410,
        "left": 250,
        "label": "Minesweeper",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/minesweeper/"
    },
    {
        "backColor": "#ffffeb",
        "fontSize": "medium",
        "header": "color-picker",
        "headerColor": "#0000ff",
        "iconName": "cb-color-palette",
        "iconColor": "#db00be",
        "iconSVG": "",
        "labelAlign": "left",
        "labelClass": "",
        "labelColor": "#555555",
        "scaleX": 3,
        "scaleY": 1,
        "selectPanel": "",
        "top": 140,
        "left": 10,
        "label": "Color picker",
        "_id": "item:01J3FHXHBP4GNXJEP29KQCQ0KD",
        "link": "https://foxess.ru/fx/color-oklch-picker/"
    }
]
